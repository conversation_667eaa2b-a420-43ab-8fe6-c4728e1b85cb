<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\Allocation;
use App\classes\AllocationItem;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/AllocationItem.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['allocation_id'])) {
            $idallocation = $_SESSION['allocation_id'];
        } elseif (isset($_GET['id'])) { // Allow loading via GET parameter as well
             $idallocation = limpiar_datos($_GET['id']);
             $_SESSION['allocation_id'] = $idallocation; // Store it in session if passed via GET
        } else {
            header('Location: allocations');
            exit();
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region get_allocation_items_data
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'get_allocation_items_data') {
    try {
        $idallocation = limpiar_datos($_POST['idallocation']);

        // Load Allocation and AllocationItems for partial refresh
        $modallocation = Allocation::get($idallocation, $conexion);
        if (!$modallocation) {
            throw new Exception("Allocation not found");
        }

        $listaAllocationItems = AllocationItem::getByAllocation($idallocation, $conexion);

        // Calculate statistics
        $totalAllocationItems = count($listaAllocationItems ?? []);
        $sumaPorcentajes = 0;
        $sumaValores = 0;
        $sumaValorBolsillo = 0;

        // Prepare items data for JSON response
        $itemsData = [];
        if (!empty($listaAllocationItems)) {
            foreach ($listaAllocationItems as $item) {
                $sumaPorcentajes += $item->getPorcentaje() ?? 0;
                $sumaValores += $item->getValor() ?? 0;
                $sumaValorBolsillo += $item->getValorBolsillo() ?? 0;

                $itemsData[] = [
                    'id' => $item->getId(),
                    'nombre' => $item->getNombre() ?? '',
                    'porcentaje' => $item->getPorcentaje() ?? 0,
                    'valor' => $item->getValor() ?? 0,
                    'valor_bolsillo' => $item->getValorBolsillo() ?? 0
                ];
            }
        }

        // Return JSON data
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'data' => [
                'items' => $itemsData,
                'totalItems' => $totalAllocationItems,
                'sumaPorcentajes' => $sumaPorcentajes,
                'sumaValores' => $sumaValores,
                'sumaValorBolsillo' => $sumaValorBolsillo
            ]
        ]);
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        exit();
    }
}
#endregion get_allocation_items_data

#region update_allocation_item_percentage
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_allocation_item_percentage') {
    try {
        $idallocationitem = limpiar_datos($_POST['idallocationitem']);
        $newPorcentaje = (float)limpiar_datos($_POST['porcentaje']);

        if (empty($idallocationitem)) {
            throw new Exception("ID de item de asignación inválido.");
        }

        // Validate percentage range
        if ($newPorcentaje < 0 || $newPorcentaje > 100) {
            throw new Exception("El porcentaje debe estar entre 0 y 100.");
        }

        // Get the existing allocation item
        $allocationItem = AllocationItem::get($idallocationitem, $conexion);
        if (!$allocationItem) {
            throw new Exception("Item de asignación no encontrado.");
        }

        // Update only the percentage
        $allocationItem->setPorcentaje($newPorcentaje);
        $allocationItem->guardar($conexion);

        // Return JSON response for AJAX
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Porcentaje actualizado correctamente',
            'newPorcentaje' => $newPorcentaje
        ]);
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        exit();
    }
}
#endregion update_allocation_item_percentage

#region update_allocation_item_valor
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_allocation_item_valor') {
    try {
        $idallocationitem = limpiar_datos($_POST['idallocationitem']);
        $newValor = (float)limpiar_datos($_POST['valor']);

        if (empty($idallocationitem)) {
            throw new Exception("ID de item de asignación inválido.");
        }

        // Get the existing allocation item
        $allocationItem = AllocationItem::get($idallocationitem, $conexion);
        if (!$allocationItem) {
            throw new Exception("Item de asignación no encontrado.");
        }

        // Update only the valor
        $allocationItem->setValor($newValor);
        $allocationItem->guardar($conexion);

        // Return JSON response for AJAX
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Valor actualizado correctamente',
            'newValor' => $newValor
        ]);
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        exit();
    }
}
#endregion update_allocation_item_valor

#region update_allocation_item_valor_bolsillo_added
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_allocation_item_valor_bolsillo_added') {
    try {
        $idallocationitem = limpiar_datos($_POST['idallocationitem']);
        $newValorBolsilloAdded = limpiar_datos($_POST['valor_bolsillo_added']);

        if (empty($idallocationitem)) {
            throw new Exception("ID de item de asignación inválido.");
        }

        // Clean and validate the numeric input
        $newValorBolsilloAdded = format_numberclean($newValorBolsilloAdded);
        if (!is_numeric($newValorBolsilloAdded)) {
            throw new Exception("El valor debe ser numérico.");
        }
        $newValorBolsilloAdded = (float)$newValorBolsilloAdded;

        // Get the existing allocation item
        $allocationItem = AllocationItem::get($idallocationitem, $conexion);
        if (!$allocationItem) {
            throw new Exception("Item de asignación no encontrado.");
        }

        // Update only the valor_bolsillo_added
        $allocationItem->setValorBolsilloAdded($newValorBolsilloAdded);
        $allocationItem->guardar($conexion);

        // Return JSON response for AJAX
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Valor bolsillo agregado actualizado correctamente',
            'newValorBolsilloAdded' => $newValorBolsilloAdded
        ]);
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        exit();
    }
}
#endregion update_allocation_item_valor_bolsillo_added

#region update_allocation_item_valor_bolsillo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_allocation_item_valor_bolsillo') {
    try {
        $idallocationitem = limpiar_datos($_POST['idallocationitem']);
        $newValorBolsillo = limpiar_datos($_POST['valor_bolsillo']);

        if (empty($idallocationitem)) {
            throw new Exception("ID de item de asignación inválido.");
        }

        // Clean and validate the numeric input
        $newValorBolsillo = format_numberclean($newValorBolsillo);
        if (!is_numeric($newValorBolsillo)) {
            throw new Exception("El valor debe ser numérico.");
        }
        $newValorBolsillo = (float)$newValorBolsillo;

        // Get the existing allocation item
        $allocationItem = AllocationItem::get($idallocationitem, $conexion);
        if (!$allocationItem) {
            throw new Exception("Item de asignación no encontrado.");
        }

        // Update only the valor_bolsillo (CRITICAL: do NOT touch valor_bolsillo_added)
        $allocationItem->setValorBolsillo($newValorBolsillo);
        $allocationItem->guardar($conexion);

        // Return JSON response for AJAX
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Valor bolsillo actualizado correctamente',
            'newValorBolsillo' => $newValorBolsillo
        ]);
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        exit();
    }
}
#endregion update_allocation_item_valor_bolsillo

#region save_allocations
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'save_allocations') {
    try {
        // Get all active AllocationItem records
        $allItems = AllocationItem::getList($conexion);

        if (empty($allItems)) {
            throw new Exception("No hay items de asignación para procesar.");
        }

        $updatedCount = 0;
        $errors = [];

        // Process each item
        foreach ($allItems as $item) {
            try {
                $currentBolsillo = (float)($item->getValorBolsillo() ?? 0);
                $currentAgregar = (float)($item->getValorBolsilloAdded() ?? 0);

                // Calculate new bolsillo value: bolsillo = bolsillo + agregar
                $newBolsillo = $currentBolsillo + $currentAgregar;

                // Update the item
                $item->setValorBolsillo($newBolsillo);
                // Note: valor_bolsillo_added is intentionally NOT modified to preserve its historical value

                // Save the changes
                if ($item->guardar($conexion)) {
                    $updatedCount++;
                } else {
                    $errors[] = "Error guardando item: " . ($item->getNombre() ?? 'Sin nombre');
                }

            } catch (Exception $e) {
                $errors[] = "Error procesando item " . ($item->getNombre() ?? 'Sin nombre') . ": " . $e->getMessage();
            }
        }

        // Prepare response message
        if ($updatedCount > 0) {
            $message = "Se actualizaron {$updatedCount} items de asignación correctamente.";
            if (!empty($errors)) {
                $message .= " Algunos items tuvieron errores: " . implode(", ", $errors);
            }

            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => $message,
                'updated_count' => $updatedCount,
                'errors' => $errors
            ]);
        } else {
            throw new Exception("No se pudo actualizar ningún item. Errores: " . implode(", ", $errors));
        }
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
        exit();
    }
}
#endregion save_allocations

#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $idallocation = limpiar_datos($_POST['idallocation']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo

#region sub_mod_allocation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_mod_allocation'])) {
    try {
        // Fetch the existing allocation first
        $allocationExistente = Allocation::get($idallocation, $conexion);
        if (!$allocationExistente) {
            throw new Exception("No se encontró la asignación con ID '{$idallocation}' para modificar.");
        }

        // Update only the fields from the form
        $allocationExistente->setNombre(limpiar_datos($_POST['nombre']));
        $allocationExistente->setPorcentaje((float)limpiar_datos($_POST['porcentaje']));

        // Save the modified object
        $allocationExistente->guardar($conexion);

        // Return JSON response for AJAX
        if (isset($_POST['ajax_check'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'success', 'message' => 'Asignación actualizada correctamente']);
            exit();
        }

        header('Location: allocations?m=1');
        exit();

    } catch (Exception $e) {
        if (isset($_POST['ajax_check'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
            exit();
        }
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_mod_allocation

#region sub_add_allocation_item
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add_allocation_item'])) {
    try {
        // Get data from form
        $idallocation_item = limpiar_datos($_POST['idallocation']);
        $item_nombre = limpiar_datos($_POST['item_nombre']);
        $item_porcentaje = (float)limpiar_datos($_POST['item_porcentaje']);
        $item_valor = (float)limpiar_datos($_POST['item_valor']);

        // Validate allocation exists
        $allocationParaItem = Allocation::get($idallocation_item, $conexion);
        if (!$allocationParaItem) {
            throw new Exception("No se encontró la asignación asociada.");
        }

        // Create and save the new allocation item
        $nuevoItem = new AllocationItem();
        $nuevoItem->setIdAllocation($idallocation_item);
        $nuevoItem->setNombre($item_nombre);
        $nuevoItem->setPorcentaje($item_porcentaje);
        $nuevoItem->setValor($item_valor);
        $nuevoItem->guardar($conexion);

        // Return JSON response for AJAX
        if (isset($_POST['ajax_check'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'success', 'message' => 'Item de asignación agregado correctamente']);
            exit();
        }

        // Set session variable to reload the correct allocation page
        $_SESSION['allocation_id'] = $idallocation_item;
        header('Location: editar-allocation');
        exit();

    } catch (Exception $e) {
        if (isset($_POST['ajax_check'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
            exit();
        }
        $error_display = 'show';
        $error_text = $e->getMessage();
        // Ensure $idallocation is set for page reload even on error
        if (isset($idallocation_item)) {
            $idallocation = $idallocation_item;
        } elseif (!isset($idallocation) && isset($_POST['idallocation'])) {
             $idallocation = limpiar_datos($_POST['idallocation']);
        }
    }
}
#endregion sub_add_allocation_item

#region sub_del_allocation_item
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_del_allocation_item'])) {
    try {
        $idallocationitem = limpiar_datos($_POST['idallocationitem']);
        $idallocation_del = limpiar_datos($_POST['idallocation']);

        if (empty($idallocationitem)) {
            throw new Exception("ID de item de asignación inválido.");
        }

        // Delete the allocation item
        AllocationItem::delete($idallocationitem, $conexion);

        // Return JSON response for AJAX
        if (isset($_POST['ajax_check'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'success', 'message' => 'Item de asignación eliminado correctamente']);
            exit();
        }

        // Set session variable to reload the correct allocation page
        $_SESSION['allocation_id'] = $idallocation_del;
        header('Location: editar-allocation');
        exit();

    } catch (Exception $e) {
        if (isset($_POST['ajax_check'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
            exit();
        }
        $error_display = 'show';
        $error_text = $e->getMessage();
        // Ensure $idallocation is set for page reload even on error
        if (isset($idallocation_del)) {
            $idallocation = $idallocation_del;
        } elseif (!isset($idallocation) && isset($_POST['idallocation'])) {
            $idallocation = limpiar_datos($_POST['idallocation']);
        }
    }
}
#endregion sub_del_allocation_item

#region try_load_data
try {
    // Ensure $idallocation is set before trying to load
    if (!isset($idallocation) && isset($_SESSION['allocation_id'])) {
        $idallocation = $_SESSION['allocation_id'];
    }

    if (empty($idallocation)) {
         header('Location: allocations');
         exit();
    }

    // Load Allocation
    $modallocation = Allocation::get($idallocation, $conexion);
    if (!$modallocation) {
        // Allocation not found, redirect
        unset($_SESSION['allocation_id']);
        header('Location: allocations?e=notfound');
        exit();
    }

    // Load AllocationItems associated with the Allocation
    $listaAllocationItems = AllocationItem::getByAllocation($idallocation, $conexion);

    // Calculate allocation item statistics
    $totalAllocationItems = count($listaAllocationItems ?? []);
    $sumaPorcentajes = 0;
    $sumaValores = 0;
    $sumaValorBolsillo = 0;
    if (!empty($listaAllocationItems)) {
        foreach ($listaAllocationItems as $item) {
            $sumaPorcentajes += $item->getPorcentaje() ?? 0;
            $sumaValores += $item->getValor() ?? 0;
            $sumaValorBolsillo += $item->getValorBolsillo() ?? 0;
        }
    }

    // Now we can unset the session variable as data is loaded
    unset($_SESSION['allocation_id']);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = "Error cargando datos: " . $e->getMessage();
    $modallocation = null;
    $listaAllocationItems = [];
}
#endregion try_load_data

require_once __ROOT__ . '/views/eallocations.view.php';

?>
